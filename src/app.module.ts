import { Module, OnModuleInit } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { DatabaseModule } from './database/database.module';
import { ValuationsModule } from './valuations/valuations.module';
import { VehiclesModule } from './vehicles/vehicles.module';
import { TasksModule } from './tasks/tasks.module';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { SeedsModule } from './database/seeds/seeds.module';
import { PermissionsSeeder } from './database/seeds/permissions.seed';
import { RolesSeeder } from './database/seeds/roles.seed';
import { AdminSeeder } from './database/seeds/admin.seed';
import { APP_GUARD } from '@nestjs/core';
import { VehicleMakeModule } from './vehicle-make/vehicle-make.module';
import { VehicleModelModule } from './vehicle-model/vehicle-model.module';
import { VehicleTypeModule } from './vehicle-type/vehicle-type.module';
import { VehicleBodyTypeModule } from './vehicle-body-type/vehicle-body-type.module';
import { VehicleFuelTypeModule } from './vehicle-fuel-type/vehicle-fuel-type.module';
import { VehicleTransmissionModule } from './vehicle-transmission/vehicle-transmission.module';
import { VehicleLightingModule } from './vehicle-lighting/vehicle-lighting.module';
import { ClientModule } from './client/client.module';
import { CompanyModule } from './company/company.module';
import { TenantModule } from './tenant/tenant.module';

@Module({
    imports: [
        ConfigModule.forRoot({
            isGlobal: true,
        }),
        ThrottlerModule.forRoot([{
            ttl: 60,
            limit: 100,
        }]),
        DatabaseModule,
        AuthModule,
        UsersModule,
        ValuationsModule,
        VehiclesModule,
        TasksModule,
        SeedsModule,
        VehicleMakeModule,
        VehicleModelModule,
        VehicleTypeModule,
        VehicleBodyTypeModule,
        VehicleFuelTypeModule,
        VehicleTransmissionModule,
        VehicleLightingModule,
        ClientModule,
        CompanyModule,
        TenantModule,
    ],
    providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule implements OnModuleInit {
    constructor(
        private readonly permissionsSeeder: PermissionsSeeder,
        private readonly adminSeeder: AdminSeeder,
        private readonly rolesSeeder: RolesSeeder,
    ) {
    }

    async onModuleInit() {
        // Run permissions seeder first
        await this.permissionsSeeder.seed();

        // Then run roles seeder
        await this.rolesSeeder.seed();

        // Then run admin seeder
        await this.adminSeeder.seed();
    }
}
