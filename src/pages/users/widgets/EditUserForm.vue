<script setup lang="ts">
import { PropType, computed, ref, watch, onMounted } from 'vue'
import { useForm } from 'vuestic-ui'
import { User, Role, Tenant, Company } from '../types'
import UserAvatar from './UserAvatar.vue'
import { validators } from '../../../services/utils'
import api from '../../../services/api'
import { authService } from '../../../services/auth.service'
import axios from 'axios'

const props = defineProps({
  user: {
    type: Object as PropType<User | null>,
    default: null,
  },
  roles: {
    type: Array as PropType<Role[]>,
    default: () => [],
  },
  saveButtonLabel: {
    type: String,
    default: 'Save',
  },
})

const defaultNewUser: Omit<User, 'id' | 'createdAt' | 'updatedAt'> & { password?: string } = {
  avatar: '',
  firstName: '',
  lastName: '',
  username: '',
  email: '',
  phone: '',
  password: '',
  isActive: true,
  roles: [],
}

const newUser = ref<User>({ ...defaultNewUser } as User)
const availableRoles = ref<Role[]>([])
const availableTenants = ref<Tenant[]>([])
const availableCompanies = ref<Company[]>([])

// Fetch available roles
const fetchRoles = async () => {
  if (props.roles && props.roles.length > 0) {
    availableRoles.value = props.roles
    return
  }

  try {
    const response = await axios.get(api.allRoles(), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + authService.getAuthToken(),
      },
    })
    availableRoles.value = response.data
  } catch (error) {
    console.error('Error fetching roles:', error)
  }
}

// Fetch available tenants
const fetchTenants = async () => {
  try {
    const response = await axios.get(api.allTenants(), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + authService.getAuthToken(),
      },
    })
    availableTenants.value = response.data
  } catch (error) {
    console.error('Error fetching tenants:', error)
  }
}

// Fetch available companies
const fetchCompanies = async () => {
  try {
    const response = await axios.get(api.allCompanies(), {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + authService.getAuthToken(),
      },
    })
    availableCompanies.value = response.data
  } catch (error) {
    console.error('Error fetching companies:', error)
  }
}

onMounted(() => {
  fetchRoles()
  fetchTenants()
  fetchCompanies()
})

// Watch for changes in roles prop
watch(() => props.roles, () => {
  if (props.roles && props.roles.length > 0) {
    availableRoles.value = props.roles
  }
}, { immediate: true })

const isFormHasUnsavedChanges = computed(() => {
  return Object.keys(newUser.value).some((key) => {
    if (key === 'avatar' || key === 'id' || key === 'createdAt' || key === 'updatedAt') {
      return false
    }

    return (
      newUser.value[key as keyof Omit<User, 'id' | 'createdAt' | 'updatedAt'>] !==
      (props.user ?? defaultNewUser)?.[key as keyof Omit<User, 'id' | 'createdAt' | 'updatedAt'>]
    )
  })
})

defineExpose({
  isFormHasUnsavedChanges,
})

watch(
  () => props.user,
  () => {
    if (props.user) {
      newUser.value = {
        ...props.user,
        avatar: props.user.avatar || '',
      }
    } else {
      newUser.value = { ...defaultNewUser } as User
    }
  },
  { immediate: true },
)

const avatar = ref<File>()

const makeAvatarBlobUrl = (avatar: File) => {
  return URL.createObjectURL(avatar)
}

watch(avatar, (newAvatar) => {
  newUser.value.avatar = newAvatar ? makeAvatarBlobUrl(newAvatar) : ''
})

const form = useForm('add-user-form')

const emit = defineEmits(['close', 'save'])

const onSave = () => {
  if (form.validate()) {
    emit('save', newUser.value)
  }
}

const selectedRoleIds = computed({
  get: () => newUser.value.roles?.map(r => r.id) || [],
  set: (value: string[]) => {
    newUser.value.roles = availableRoles.value.filter(r => value.includes(r.id))
  }
})

const selectedTenantIds = computed({
  get: () => newUser.value.tenants?.map(t => t.id) || [],
  set: (value: string[]) => {
    newUser.value.tenants = availableTenants.value.filter(t => value.includes(t.id))
  }
})

const selectedCompanyIds = computed({
  get: () => newUser.value.companies?.map(c => c.id) || [],
  set: (value: string[]) => {
    newUser.value.companies = availableCompanies.value.filter(c => value.includes(c.id))
  }
})
</script>

<template>
  <VaForm v-slot="{ isValid }" ref="add-user-form" class="flex-col justify-start items-start gap-4 inline-flex w-full">
    <VaFileUpload
      v-model="avatar"
      type="single"
      hide-file-list
      class="self-stretch justify-start items-center gap-4 inline-flex"
    >
      <UserAvatar :user="newUser" size="large" />
      <VaButton preset="primary" size="small">Add image</VaButton>
      <VaButton
        v-if="avatar"
        preset="primary"
        color="danger"
        size="small"
        icon="delete"
        class="z-10"
        @click.stop="avatar = undefined"
      />
    </VaFileUpload>
    <div class="self-stretch flex-col justify-start items-start gap-4 flex">
      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput
          v-model="newUser.firstName"
          label="First Name"
          class="w-full sm:w-1/2"
          :rules="[validators.required]"
          name="fullName"
        />
          <VaInput
          v-model="newUser.lastName"
          label="Last name"
          class="w-full sm:w-1/2"
          :rules="[validators.required]"
          name="fullName"
        />
      </div>
      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput
          v-model="newUser.username"
          label="Username"
          class="w-full sm:w-1/2"
          :rules="[validators.required]"
          name="username"
        />
        <VaInput
          v-model="newUser.email"
          label="Email"
          class="w-full sm:w-1/2"
          :rules="[validators.required, validators.email]"
          name="email"
        />
      </div>

      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaInput
          v-model="newUser.phone"
          label="Phone"
          class="w-full sm:w-1/2"
          name="phone"
        />
        <VaInput
          v-if="!props.user"
          v-model="newUser.password"
          label="Password"
          type="password"
          class="w-full sm:w-1/2"
          :rules="[validators.required]"
          name="password"
        />
      </div>

      <div class="flex gap-4 w-full">
        <div class="w-1/2">
          <VaSelect
            v-model="selectedRoleIds"
            label="Roles"
            class="w-full"
            :options="availableRoles"
            value-by="id"
            text-by="name"
            name="roles"
            multiple
            searchable
            :max-visible-options="3"
          />
        </div>

        <div class="flex items-center w-1/2 mt-4">
          <VaCheckbox v-model="newUser.isActive" label="Active" class="w-full" name="isActive" />
        </div>
      </div>

      <div class="flex gap-4 flex-col sm:flex-row w-full">
        <VaSelect
          v-model="selectedTenantIds"
          label="Tenants"
          class="w-full sm:w-1/2"
          :options="availableTenants"
          value-by="id"
          text-by="name"
          name="tenants"
          multiple
          searchable
          :max-visible-options="3"
        />
        <VaSelect
          v-model="selectedCompanyIds"
          label="Companies"
          class="w-full sm:w-1/2"
          :options="availableCompanies"
          value-by="id"
          text-by="name"
          name="companies"
          multiple
          searchable
          :max-visible-options="3"
        />
      </div>
      <div class="flex gap-2 flex-col-reverse items-stretch justify-end w-full sm:flex-row sm:items-center">
        <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
        <VaButton :disabled="!isValid" @click="onSave">{{ saveButtonLabel }}</VaButton>
      </div>
    </div>
  </VaForm>
</template>
