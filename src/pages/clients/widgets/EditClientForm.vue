<script setup lang="ts">
import {computed, ref, watch, onMounted} from 'vue'
import {EmptyClient, Client, ClientType, Tenant, Company} from '../types'
import {VaButton, VaForm, VaInput, VaSelect} from "vuestic-ui"
import {getAvailableTenants, getAvailableCompanies} from '../../../data/pages/clients'
import { authService } from '../../../services/auth.service'
import { jwtDecode } from 'jwt-decode'

const props = defineProps<{
    client: Client | null
    saveButtonLabel: string
    readonly?: boolean
}>()

const emit = defineEmits<{
    (event: 'save', client: Client): void
    (event: 'close'): void
}>()

const defaultNewClient: EmptyClient = {
    clientType: ClientType.INDIVIDUAL,
    companyName: '',
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    phone: '',
    altPhone: '',
    address: '',
    city: '',
    country: '',
    postalCode: '',
    nationalId: '',
    companyRegistrationNo: '',
    taxIdNumber: '',
    contactPersonName: '',
    contactPersonDesignation: '',
    tenantIds: [],
    companyIds: [],
}

const newClient = ref<Client | EmptyClient>({...defaultNewClient})

const clientTypeOptions = [
    { text: 'Individual', value: ClientType.INDIVIDUAL },
    { text: 'Company', value: ClientType.COMPANY },
]

// Available options for dropdowns
const availableTenants = ref<Tenant[]>([])
const availableCompanies = ref<Company[]>([])

// Debug reactive values
watch(() => newClient.value.clientType, (newType) => {
    console.log('Client type changed to:', newType)
}, { immediate: true })

const isFormHasUnsavedChanges = computed(() => {
    if (!props.client) {
        // Creating new - check if any field is different from empty
        return Object.keys(defaultNewClient).some(key =>
            newClient.value[key as keyof EmptyClient] !== defaultNewClient[key as keyof EmptyClient]
        )
    }
    // Editing existing - check if any field is different from original
    return Object.keys(newClient.value).some(key =>
        newClient.value[key as keyof Client] !== props.client![key as keyof Client]
    )
})

defineExpose({
    isFormHasUnsavedChanges,
})

watch(
    () => props.client,
    () => {
        if (!props.client) {
            newClient.value = {...defaultNewClient}
            return
        }

        newClient.value = {
            ...props.client,
            // Convert tenant and company objects to IDs for form
            tenantIds: props.client.tenants?.map(t => t.id) || [],
            companyIds: props.client.companies?.map(c => c.id) || [],
        }
    },
    {immediate: true},
)

const required = (v: string) => !!v || 'This field is required'
const requiredArray = (v: any[]) => (v && v.length > 0) || 'At least one selection is required'

// Conditional validation based on client type
const requiredForCompany = (v: string) => {
    if (newClient.value.clientType === 'COMPANY') {
        return !!v || 'This field is required for company clients'
    }
    return true
}

const requiredForIndividual = (v: string) => {
    if (newClient.value.clientType === 'INDIVIDUAL') {
        return !!v || 'This field is required for individual clients'
    }
    return true
}

const handleSave = () => {
    const formData = {
        ...newClient.value,
    }

    let clientToSave
    if (props.client) {
        // Editing: preserve all fields, update with form values
        clientToSave = {...props.client, ...formData}
    } else {
        // Creating: use form values
        clientToSave = formData
    }

    emit('save', clientToSave as Client)
}

// Get current user's tenant from JWT token
const getCurrentUserTenant = () => {
    try {
        const token = authService.getAuthToken()
        if (token) {
            const decoded: any = jwtDecode(token)
            // For now, we'll assume the first tenant is the user's default
            // This should be enhanced to get actual user-tenant relationships
            return decoded.tenantId || null
        }
    } catch (error) {
        console.error('Error decoding token:', error)
    }
    return null
}

// Load available options on mount
const loadOptions = async () => {
    try {
        const [tenants, companies] = await Promise.all([
            getAvailableTenants(),
            getAvailableCompanies()
        ])

        availableTenants.value = tenants
        availableCompanies.value = companies

        // Auto-select user's tenant for new clients
        if (!props.client && tenants.length > 0) {
            const userTenantId = getCurrentUserTenant()
            if (userTenantId && tenants.find(t => t.id === userTenantId)) {
                newClient.value.tenantIds = [userTenantId]
            } else {
                // If no specific tenant found, select the first one as default
                newClient.value.tenantIds = [tenants[0].id]
            }
        }

        // Auto-select first company for new clients if available
        if (!props.client && companies.length > 0) {
            newClient.value.companyIds = [companies[0].id]
        }
    } catch (error) {
        console.error('Failed to load available options:', error)
    }
}

onMounted(() => {
    loadOptions()
})
</script>

<template>
    <VaForm v-slot="{ validate }" class="flex flex-col gap-2">
        <!-- Client Type Selection -->
        <VaSelect
            v-model="newClient.clientType"
            label="Client Type"
            :options="clientTypeOptions"
            text-by="text"
            value-by="value"
            :rules="[required]"
            :readonly="props.readonly"
            class="mb-4"
        />

        <!-- Debug Info -->
        <div class="text-sm text-gray-500 mb-2">
            Current client type: {{ newClient.clientType }}
        </div>

        <!-- Company Fields (shown only for COMPANY type) -->
        <div v-if="newClient.clientType === 'COMPANY'" class="space-y-4">
            <h3 class="text-lg font-semibold text-primary">Company Information</h3>
            <VaInput
                v-model="newClient.companyName"
                label="Company Name"
                :rules="[requiredForCompany]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.companyRegistrationNo"
                label="Company Registration Number (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.contactPersonName"
                label="Contact Person Name (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.contactPersonDesignation"
                label="Contact Person Designation (Optional)"
                :readonly="props.readonly"
            />
        </div>

        <!-- Individual Fields (shown only for INDIVIDUAL type) -->
        <div v-if="newClient.clientType === 'INDIVIDUAL'" class="space-y-4">
            <h3 class="text-lg font-semibold text-primary">Personal Information</h3>
            <VaInput
                v-model="newClient.firstName"
                label="First Name"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.middleName"
                label="Middle Name (Optional)"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.lastName"
                label="Last Name"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.nationalId"
                label="National ID"
                :rules="[requiredForIndividual]"
                :readonly="props.readonly"
            />
        </div>

        <!-- Common Contact Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Contact Information</h3>
            <VaInput
                v-model="newClient.email"
                label="Email"
                type="email"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.phone"
                label="Phone"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.altPhone"
                label="Alternative Phone (Optional)"
                :readonly="props.readonly"
            />
        </div>

        <!-- Address Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Address Information</h3>
            <VaInput
                v-model="newClient.address"
                label="Street Address"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.city"
                label="City"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.country"
                label="Country"
                :rules="[required]"
                :readonly="props.readonly"
            />
            <VaInput
                v-model="newClient.postalCode"
                label="Postal Code"
                :rules="[required]"
                :readonly="props.readonly"
            />
        </div>

        <!-- Optional Information -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Additional Information (Optional)</h3>
            <VaInput
                v-model="newClient.taxIdNumber"
                label="Tax ID Number"
                :readonly="props.readonly"
            />
        </div>

        <!-- Tenant and Company Associations -->
        <div class="space-y-4 mt-6">
            <h3 class="text-lg font-semibold text-primary">Associations (Required)</h3>

            <VaSelect
                v-model="newClient.tenantIds"
                label="Associated Tenants"
                :options="availableTenants"
                text-by="name"
                value-by="id"
                multiple
                :rules="[requiredArray]"
                :readonly="props.readonly"
                placeholder="Select at least one tenant"
            />

            <VaSelect
                v-model="newClient.companyIds"
                label="Associated Companies"
                :options="availableCompanies"
                text-by="name"
                value-by="id"
                multiple
                :rules="[requiredArray]"
                :readonly="props.readonly"
                placeholder="Select at least one company"
            />
        </div>

        <!-- Action Buttons -->
        <div v-if="!props.readonly" class="flex justify-end flex-col-reverse sm:flex-row mt-6 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Cancel</VaButton>
            <VaButton @click="validate() && handleSave()">{{ saveButtonLabel }}</VaButton>
        </div>
        <div v-else class="flex justify-end flex-col-reverse sm:flex-row mt-6 gap-2">
            <VaButton preset="secondary" color="secondary" @click="$emit('close')">Close</VaButton>
        </div>
    </VaForm>
</template>
