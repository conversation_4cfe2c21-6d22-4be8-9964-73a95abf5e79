import api from '../../services/api'
import {Client} from '../../pages/clients/types'
import { authService } from '../../services/auth.service'
import axios from 'axios'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: 'name' | 'updatedAt' | 'createdAt'
    sortingOrder: 'ASC' | 'DESC' | null
}

export const getClients = async (options: Partial<Sorting> & Pagination) => {
    const response = await fetch(api.allClients(options)).then((r) => r.json())
    console.log("response:...........", response)
    return {
        data: response.data || [],
        pagination: response.meta || {page: 1, perPage: 10, total: 0},
    }
}

export const addClient = async (client: any) => {
    try {
        console.log("client:...........", client)
        const {id, createdAt, updatedAt, tenants, companies, ...rawPayload} = client

        // Convert tenantIds and companyIds to the format expected by backend
        const payload = {
            ...Object.fromEntries(
                Object.entries(rawPayload).filter(
                    ([, value]) => value !== undefined && value !== null && value !== ''
                )
            ),
            // Add tenant and company IDs if they exist
            ...(client.tenantIds && client.tenantIds.length > 0 && { tenantIds: client.tenantIds }),
            ...(client.companyIds && client.companyIds.length > 0 && { companyIds: client.companyIds })
        }
        console.log("payload:...........", payload)

        const response = await axios.post(api.allClients(), payload, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })

        console.log("response:...........", response.data)
        return response.data
    } catch (error: any) {
        console.error('Error in addClient:', error)
        // Re-throw the error so it can be handled by the calling component
        throw error
    }
}

export const updateClient = async (client: any) => {
    try {
        console.log("client:...........", client)
        const {id, companies, tenants, createdAt, updatedAt, ...rawPayload} = client

        // Convert tenant and company objects/IDs to ID arrays
        const payload = {
            ...Object.fromEntries(
                Object.entries(rawPayload).filter(
                    ([, value]) => value !== undefined && value !== null && value !== ''
                )
            ),
            // Handle both tenantIds (from form) and tenants (from existing client)
            ...(client.tenantIds && client.tenantIds.length > 0 && { tenantIds: client.tenantIds }),
            ...(tenants && tenants.length > 0 && !client.tenantIds && { tenantIds: tenants.map(t => t.id) }),
            // Handle both companyIds (from form) and companies (from existing client)
            ...(client.companyIds && client.companyIds.length > 0 && { companyIds: client.companyIds }),
            ...(companies && companies.length > 0 && !client.companyIds && { companyIds: companies.map(c => c.id) })
        }
        console.log("payload:...........", payload)

        const response = await axios.patch(api.client(id), payload, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + authService.getAuthToken(),
            },
        })

        console.log("response:...........", response.data)
        return response.data
    } catch (error: any) {
        console.error('Error in updateClient:', error)
        // Re-throw the error so it can be handled by the calling component
        throw error
    }
}


export const removeClient = async (client: Client) => {
    const response = await axios.delete(api.client(client.id), {
        headers: {
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    })
    return response.status === 200
}

export const getAvailableTenants = async () => {
    const response = await axios.get(api.clientTenants(), {
        headers: {
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    })
    return response.data
}

export const getAvailableCompanies = async () => {
    const response = await axios.get(api.clientCompanies(), {
        headers: {
            'Authorization': 'Bearer ' + authService.getAuthToken(),
        },
    })
    return response.data
}
