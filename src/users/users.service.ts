import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './entities/user.entity';
import { Role } from './entities/role.entity';
import { Permission } from './entities/permission.entity';
import { Tenant } from '../tenant/entities/tenant.entity';
import { Company } from '../company/entities/company.entity';
import { CreateRoleDto } from './dto/create-role.dto';
import { UpdateRoleDto } from './dto/update-role.dto';
import { CreatePermissionDto } from './dto/create-permission.dto';
import { UpdatePermissionDto } from './dto/update-permission.dto';
import { UserResponseDto } from './dto/user-response.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class UsersService {
    constructor(
        @InjectRepository(User) private readonly usersRepository: Repository<User>,
        @InjectRepository(Role) private readonly rolesRepository: Repository<Role>,
        @InjectRepository(Permission) private readonly permissionsRepository: Repository<Permission>,
        @InjectRepository(Tenant) private readonly tenantsRepository: Repository<Tenant>,
        @InjectRepository(Company) private readonly companiesRepository: Repository<Company>,
    ) {
    }

    async create(createUserDto: CreateUserDto): Promise<UserResponseDto> {
        const existingUser = await this.usersRepository.findOne({
            where: { email: createUserDto.email },
        });

        if (existingUser) {
            throw new ConflictException('User with this email already exists');
        }

        let guestRole = await this.rolesRepository.findOne({
            where: { name: 'Guest' },
            relations: ['permissions'],
        });
        if (!guestRole) {
            guestRole = this.rolesRepository.create({
                name: 'Guest',
                description: 'Standard user with limited access to read.',
            });
            guestRole = await this.rolesRepository.save(guestRole);
        }

        // Handle roles
        let roles = [guestRole];
        if (createUserDto.roleIds && createUserDto.roleIds.length > 0) {
            const userRoles = await this.rolesRepository.findByIds(createUserDto.roleIds);
            if (userRoles.length !== createUserDto.roleIds.length) {
                throw new NotFoundException('One or more roles not found');
            }
            roles = userRoles;
        }

        // Handle tenants
        let tenants = [];
        if (createUserDto.tenantIds && createUserDto.tenantIds.length > 0) {
            tenants = await this.tenantsRepository.findByIds(createUserDto.tenantIds);
            if (tenants.length !== createUserDto.tenantIds.length) {
                throw new NotFoundException('One or more tenants not found');
            }
        }

        // Handle companies
        let companies = [];
        if (createUserDto.companyIds && createUserDto.companyIds.length > 0) {
            companies = await this.companiesRepository.findByIds(createUserDto.companyIds);
            if (companies.length !== createUserDto.companyIds.length) {
                throw new NotFoundException('One or more companies not found');
            }
        }

        const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

        // Remove the IDs from the DTO before creating the user
        const { roleIds, tenantIds, companyIds, ...userCreateData } = createUserDto;

        const user = this.usersRepository.create({
            ...userCreateData,
            password: hashedPassword,
            roles,
            tenants,
            companies,
        });

        const savedUser = await this.usersRepository.save(user);
        const { password, ...newUser } = savedUser;

        return newUser as UserResponseDto;
    }

    async findAll(): Promise<UserResponseDto[]> {
        const users = await this.usersRepository.find({
            relations: ['roles', 'roles.permissions', 'tenants', 'companies'],
        });
        return users.map(user => {
            const { password, ...userWithoutPassword } = user;
            return userWithoutPassword as UserResponseDto;
        });
    }

    async findOne(id: string): Promise<UserResponseDto> {
        const user = await this.usersRepository.findOne({
            where: { id },
            relations: ['roles', 'roles.permissions', 'tenants', 'companies'],
        });

        if (!user) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }

        const { password, ...userWithoutPassword } = user;
        return userWithoutPassword as UserResponseDto;
    }

    async findOneByEmail(email: string): Promise<User | null> {
        return this.usersRepository.findOne({
            where: { email },
            relations: ['roles', 'roles.permissions', 'tenants', 'companies'],
        });
    }

    async update(id: string, updateUserDto: UpdateUserDto): Promise<UserResponseDto> {
        const user = await this.usersRepository.findOne({
            where: { id },
            relations: ['roles', 'tenants', 'companies'],
        });

        if (!user) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }

        if (updateUserDto.password) {
            updateUserDto.password = await bcrypt.hash(updateUserDto.password, 10);
        }

        if (updateUserDto.roleIds) {
            const roles = await this.rolesRepository.findByIds(updateUserDto.roleIds);
            if (roles.length !== updateUserDto.roleIds.length) {
                throw new NotFoundException('One or more roles not found');
            }
            user.roles = roles;
        }

        if (updateUserDto.tenantIds) {
            const tenants = await this.tenantsRepository.findByIds(updateUserDto.tenantIds);
            if (tenants.length !== updateUserDto.tenantIds.length) {
                throw new NotFoundException('One or more tenants not found');
            }
            user.tenants = tenants;
        }

        if (updateUserDto.companyIds) {
            const companies = await this.companiesRepository.findByIds(updateUserDto.companyIds);
            if (companies.length !== updateUserDto.companyIds.length) {
                throw new NotFoundException('One or more companies not found');
            }
            user.companies = companies;
        }

        // Remove the IDs from the DTO before assigning to avoid validation errors
        const { roleIds, tenantIds, companyIds, ...userUpdateData } = updateUserDto;
        Object.assign(user, userUpdateData);

        const savedUser = await this.usersRepository.save(user);
        const { password, ...updatedUser } = savedUser;
        return updatedUser as UserResponseDto;
    }

    async remove(id: string): Promise<void> {
        const result = await this.usersRepository.softDelete(id);

        if (result.affected === 0) {
            throw new NotFoundException(`User with ID ${id} not found`);
        }
    }

    async deactivate(id: string): Promise<User> {
        const user = await this.findOne(id);
        user.isActive = false;
        user.updatedAt = new Date();
        return this.usersRepository.save(user);
    }

    async activate(id: string): Promise<User> {
        const user = await this.findOne(id);
        user.isActive = true;
        user.updatedAt = new Date();
        return this.usersRepository.save(user);
    }

    // Role management methods
    async createRole(createRoleDto: CreateRoleDto): Promise<Role> {
        const { name, permissionIds } = createRoleDto;

        const existingRole = await this.rolesRepository.findOne({ where: { name } });
        if (existingRole) {
            throw new ConflictException('Role with this name already exists');
        }

        let permissions: Permission[] = [];
        if (permissionIds && permissionIds.length > 0) {
            permissions = await this.permissionsRepository.findByIds(permissionIds);
            if (permissions.length !== permissionIds.length) {
                throw new NotFoundException('One or more permissions not found');
            }
        }

        const role = this.rolesRepository.create({
            ...createRoleDto,
            permissions,
        });

        return this.rolesRepository.save(role);
    }

    async findAllRoles(): Promise<Role[]> {
        return this.rolesRepository.find({
            relations: ['permissions'],
            order: {
                name: 'ASC',
            },
        });
    }

    async findRole(id: string): Promise<Role> {
        const role = await this.rolesRepository.findOne({
            where: { id },
            relations: ['permissions'],
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }

        return role;
    }

    async updateRole(id: string, updateRoleDto: UpdateRoleDto): Promise<Role> {
        const role = await this.findRole(id);
        const { name, permissionIds } = updateRoleDto;

        if (name && name !== role.name) {
            const existingRole = await this.rolesRepository.findOne({ where: { name } });
            if (existingRole) {
                throw new ConflictException('Role with this name already exists');
            }
        }

        if (permissionIds) {
            const permissions = await this.permissionsRepository.findByIds(permissionIds);
            if (permissions.length !== permissionIds.length) {
                throw new NotFoundException('One or more permissions not found');
            }
            role.permissions = permissions;
        }

        Object.assign(role, updateRoleDto);
        return this.rolesRepository.save(role);
    }

    async removeRole(id: string): Promise<{ message: string }> {
        const role = await this.findRole(id);
        if (!role) {
            throw new NotFoundException(`Role with ID ${id} not found`);
        }
        await this.rolesRepository.softDelete(id);
        return { message: `Role with ID ${id} has been soft deleted` };
    }

    // Permission management methods
    async createPermission(createPermissionDto: CreatePermissionDto): Promise<Permission> {
        const { name } = createPermissionDto;

        const existingPermission = await this.permissionsRepository.findOne({ where: { name } });
        if (existingPermission) {
            throw new ConflictException('Permission with this name already exists');
        }

        const permission = this.permissionsRepository.create(createPermissionDto);
        return this.permissionsRepository.save(permission);
    }

    async findAllPermissions(): Promise<Permission[]> {
        return this.permissionsRepository.find({
            order: {
                module: 'ASC',
                action: 'ASC',
            },
        });
    }

    async findPermission(id: string): Promise<Permission> {
        const permission = await this.permissionsRepository.findOne({
            where: { id },
        });

        if (!permission) {
            throw new NotFoundException(`Permission with ID ${id} not found`);
        }

        return permission;
    }

    async updatePermission(id: string, updatePermissionDto: UpdatePermissionDto): Promise<Permission> {
        const permission = await this.findPermission(id);
        const { name } = updatePermissionDto;

        if (name && name !== permission.name) {
            const existingPermission = await this.permissionsRepository.findOne({ where: { name } });
            if (existingPermission) {
                throw new ConflictException('Permission with this name already exists');
            }
        }

        Object.assign(permission, updatePermissionDto);
        return this.permissionsRepository.save(permission);
    }

    async removePermission(id: string): Promise<{ message: string }> {
        const permission = await this.findPermission(id);

        if (!permission) {
            throw new NotFoundException(`Permission with ID ${id} not found`);
        }

        await this.permissionsRepository.softDelete(id);

        return { message: `Permission with ID ${id} has been soft deleted` };
    }

    async hasPermission(userId: string, permissionName: string): Promise<boolean> {
        const user = await this.findOne(userId);
        return user.roles.some(role =>
            role.permissions.some(permission => permission.name === permissionName),
        );
    }

    async assignRole(userId: string, roleId: string): Promise<UserResponseDto> {
        const user = await this.findOne(userId);
        const role = await this.rolesRepository.findOne({
            where: { id: roleId },
            relations: ['permissions'],
        });

        if (!role) {
            throw new NotFoundException(`Role with ID ${roleId} not found`);
        }

        if (!user.roles) {
            user.roles = [];
        }

        if (!user.roles.some(r => r.id === role.id)) {
            user.roles.push(role);
        }

        const { password, ...updatedUser } = await this.usersRepository.save(user);
        return updatedUser as UserResponseDto;
    }

    async removeRoleFromUser(userId: string, roleId: string): Promise<UserResponseDto> {
        const user = await this.findOne(userId);
        user.roles = user.roles.filter(role => role.id !== roleId);
        const { password, ...updatedUser } = await this.usersRepository.save(user);
        return updatedUser as UserResponseDto;
    }

    async getUserPermissions(userId: string): Promise<Permission[]> {
        const user = await this.findOne(userId);
        const permissions = new Set<Permission>();

        user.roles.forEach(role => {
            role.permissions.forEach(permission => {
                permissions.add(permission);
            });
        });

        return Array.from(permissions);
    }
}
